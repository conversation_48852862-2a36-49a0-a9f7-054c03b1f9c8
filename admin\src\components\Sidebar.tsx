import React, { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Home,
  FileText,
  TrendingUp,
  Bell,
  MessageSquare,
  BarChart3,
  Wallet,
  Users,
  ChevronDown,
  ChevronRight,
  X,
  Package,
  ShoppingCart,
  UserCheck,
  PieChart,
  Send,
  History,
  Edit,
} from 'lucide-react';
import clsx from 'clsx';

interface SubMenuItem {
  id: string;
  label: string;
  path: string;
  icon?: React.ComponentType<{ className?: string }>;
}

interface MenuItem {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  path?: string;
  subItems?: SubMenuItem[];
}

const menuItems: MenuItem[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: Home,
    path: '/dashboard',
  },
  {
    id: 'pages',
    label: 'Pages',
    icon: FileText,
    subItems: [
      { id: 'homepage', label: 'Homepage', path: '/pages/homepage' },
      { id: 'products', label: 'Products', path: '/pages/products' },
      { id: 'about', label: 'About Us', path: '/pages/about' },
      { id: 'contact', label: 'Contact Us', path: '/pages/contact' },
    ],
  },
  {
    id: 'revenue',
    label: 'Revenue',
    icon: TrendingUp,
    subItems: [
      { id: 'sales-overview', label: 'Sales Overview', path: '/revenue/sales-overview', icon: PieChart },
      { id: 'revenue-products', label: 'Products', path: '/revenue/products', icon: Package },
      { id: 'brokers', label: 'Brokers', path: '/revenue/brokers', icon: UserCheck },
      { id: 'referral', label: 'Referral', path: '/revenue/referral', icon: Users },
    ],
  },
  {
    id: 'notifications',
    label: 'Notifications',
    icon: Bell,
    path: '/notifications',
  },
  {
    id: 'messages',
    label: 'Messages',
    icon: MessageSquare,
    subItems: [
      { id: 'compose', label: 'Compose', path: '/messages/compose', icon: Edit },
      { id: 'received', label: 'Received Messages', path: '/messages/received', icon: MessageSquare },
      { id: 'history', label: 'Message History', path: '/messages/history', icon: History },
    ],
  },
  {
    id: 'analytics',
    label: 'Analytics',
    icon: BarChart3,
    path: '/analytics',
  },
  {
    id: 'wallets',
    label: 'Wallets',
    icon: Wallet,
    path: '/wallets',
  },
  {
    id: 'auth',
    label: 'Auth',
    icon: Users,
    subItems: [
      { id: 'clients', label: 'Clients', path: '/auth/clients', icon: Users },
      { id: 'broker', label: 'Broker', path: '/auth/broker', icon: UserCheck },
      { id: 'admin', label: 'Admin', path: '/auth/admin', icon: UserCheck },
    ],
  },
];

interface SidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({ isOpen, onClose }) => {
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());
  const location = useLocation();
  const navigate = useNavigate();

  const toggleExpanded = (itemId: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(itemId)) {
      newExpanded.delete(itemId);
    } else {
      newExpanded.add(itemId);
    }
    setExpandedItems(newExpanded);
  };

  const handleItemClick = (item: MenuItem) => {
    if (item.subItems) {
      toggleExpanded(item.id);
    } else if (item.path) {
      navigate(item.path);
      onClose();
    }
  };

  const handleSubItemClick = (subItem: SubMenuItem) => {
    navigate(subItem.path);
    onClose();
  };

  const isItemActive = (path: string) => {
    return location.pathname === path;
  };

  const isParentActive = (item: MenuItem) => {
    if (item.path && isItemActive(item.path)) return true;
    if (item.subItems) {
      return item.subItems.some(subItem => isItemActive(subItem.path));
    }
    return false;
  };

  const sidebarVariants = {
    open: { x: 0 },
    closed: { x: '-100%' },
  };

  return (
    <>
      {/* Mobile Overlay */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-40 bg-black/50 lg:hidden"
            onClick={onClose}
          />
        )}
      </AnimatePresence>

      {/* Sidebar */}
      <motion.aside
        initial="closed"
        animate={isOpen ? "open" : "closed"}
        variants={sidebarVariants}
        transition={{ type: "spring", stiffness: 300, damping: 30 }}
        className={clsx(
          "fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-xl lg:static lg:translate-x-0",
          "lg:block border-r border-gray-200"
        )}
        style={{
          background: 'linear-gradient(180deg, #ffffff 0%, #f8fafc 100%)'
        }}
      >
        <div className="flex h-full flex-col">
          {/* Logo */}
          <div className="flex h-16 items-center justify-between px-6 border-b border-gray-200">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">PK</span>
              </div>
              <span className="ml-2 text-xl font-bold text-gray-900">PixieKat</span>
            </div>
            <button
              onClick={onClose}
              className="lg:hidden p-1 rounded-md hover:bg-gray-100 transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 py-6 space-y-1 overflow-y-auto">
            {menuItems.map((item) => {
              const isActive = isParentActive(item);
              const isExpanded = expandedItems.has(item.id);
              const Icon = item.icon;
              
              return (
                <div key={item.id} className="space-y-1">
                  {/* Main Menu Item */}
                  <motion.button
                    onClick={() => handleItemClick(item)}
                    whileHover={{ x: 2 }}
                    className={clsx(
                      "w-full flex items-center justify-between px-3 py-2.5 rounded-lg text-sm font-medium transition-all duration-200",
                      isActive
                        ? "bg-primary-600 text-white shadow-md"
                        : "text-gray-600 hover:bg-gray-100 hover:text-gray-900"
                    )}
                  >
                    <div className="flex items-center">
                      <Icon className={clsx("w-5 h-5 mr-3", isActive ? "text-white" : "text-gray-400")} />
                      {item.label}
                    </div>
                    {item.subItems && (
                      <motion.div
                        animate={{ rotate: isExpanded ? 90 : 0 }}
                        transition={{ duration: 0.2 }}
                      >
                        <ChevronRight className="w-4 h-4" />
                      </motion.div>
                    )}
                  </motion.button>

                  {/* Sub Menu Items */}
                  <AnimatePresence>
                    {item.subItems && isExpanded && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        transition={{ duration: 0.2 }}
                        className="ml-6 space-y-1 overflow-hidden"
                      >
                        {item.subItems.map((subItem) => {
                          const isSubActive = isItemActive(subItem.path);
                          const SubIcon = subItem.icon;
                          
                          return (
                            <motion.button
                              key={subItem.id}
                              onClick={() => handleSubItemClick(subItem)}
                              whileHover={{ x: 2 }}
                              className={clsx(
                                "w-full flex items-center px-3 py-2 rounded-md text-sm transition-all duration-200",
                                isSubActive
                                  ? "bg-primary-500 text-white font-medium shadow-sm"
                                  : "text-gray-500 hover:bg-gray-100 hover:text-gray-700"
                              )}
                            >
                              {SubIcon && (
                                <SubIcon className={clsx("w-4 h-4 mr-2", isSubActive ? "text-white" : "text-gray-400")} />
                              )}
                              {subItem.label}
                            </motion.button>
                          );
                        })}
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              );
            })}
          </nav>
        </div>
      </motion.aside>
    </>
  );
};

export default Sidebar;
