{"name": "PixieKat.com", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@gsap/react": "^2.1.2", "clsx": "^2.1.1", "framer-motion": "^12.15.0", "gsap": "^3.13.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "^5.3.0", "react-router-dom": "^6.26.0", "react-use": "^17.5.1", "tailwind": "^4.0.0", "axios": "^1.6.0", "jwt-decode": "^4.0.0", "lucide-react": "^0.400.0"}, "devDependencies": {"@eslint/js": "^9.13.0", "@types/node": "^22.15.29", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@vitejs/plugin-react": "^4.3.3", "autoprefixer": "^10.4.20", "eslint": "^9.14.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "eslint-plugin-tailwindcss": "^3.17.5", "globals": "^15.11.0", "postcss": "^8.4.49", "prettier": "^3.3.3", "tailwindcss": "^3.4.14", "typescript": "^5.8.3", "vite": "^5.4.10"}}