@import url("https://fonts.cdnfonts.com/css/general-sans");

@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  width: 100dvw;
  overflow-x: hidden;
  background-color: #dfdff0;
  font-family: "General Sans", sans-serif;
}
html {
  overflow-x: hidden;
}

* {
  box-sizing: border-box;
}

@layer base {
  @font-face {
    font-family: "circular-web";
    src: url("/fonts/circularweb-book.woff2") format("woff2");
  }

  @font-face {
    font-family: "general";
    src: url("/fonts/general.woff2") format("woff2");
  }

  @font-face {
    font-family: "robert-medium";
    src: url("/fonts/robert-medium.woff2") format("woff2");
  }

  @font-face {
    font-family: "robert-regular";
    src: url("/fonts/robert-regular.woff2") format("woff2");
  }

  @font-face {
    font-family: "zentry";
    src: url("/fonts/zentry-regular.woff2") format("woff2");
  }
}

@layer utilities {
  .border-hsla {
    @apply border border-white/20;
  }

  .nav-hover-btn {
    @apply relative ms-10 font-general text-xs uppercase text-blue-50 after:absolute after:-bottom-0.5 after:left-0 after:h-[2px] after:w-full after:origin-bottom-right after:scale-x-0 after:bg-neutral-800 after:transition-transform after:duration-300 after:ease-[cubic-bezier(0.65_0.05_0.36_1)] hover:after:origin-bottom-left hover:after:scale-x-100 dark:after:bg-white cursor-pointer;
  }

  .floating-nav {
    @apply bg-black rounded-lg border;
  }

  .absolute-center {
    @apply absolute top-1/2 left-1/2 translate-x-[-50%] translate-y-[-50%];
  }

  .flex-center {
    @apply flex justify-center items-center;
  }

  .mask-clip-path {
    clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
  }

  .special-font b {
    font-family: "Zentry";
    font-feature-settings: "ss01" on;
  }

  .hero-heading {
    @apply uppercase font-zentry font-black text-5xl sm:right-10 sm:text-7xl md:text-9xl lg:text-[12rem];
  }

  .about-subtext {
    @apply absolute bottom-[-80dvh] left-1/2 w-full max-w-96 -translate-x-1/2 text-center font-circular-web text-lg md:max-w-[34rem];
  }

  .about-image {
    @apply absolute left-1/2 top-0 z-20 h-[60vh] w-96 origin-center -translate-x-1/2 overflow-hidden rounded-3xl md:w-[30vw];
  }

  .animated-title {
    @apply flex flex-col gap-1 text-7xl uppercase leading-[.8] text-white sm:px-32 md:text-[6rem];
  }

  .animated-word {
    @apply special-font font-zentry font-black opacity-0;
    transform: translate3d(10px, 51px, -60px) rotateY(60deg) rotateX(-40deg);
    transform-origin: 50% 50% -150px !important;
    will-change: opacity, transform;
  }

  .bento-tilt_1 {
    @apply relative border-hsla col-span-2 overflow-hidden rounded-md transition-transform duration-300 ease-out;
  }

  .bento-tilt_2 {
    @apply relative col-span-1 row-span-1 overflow-hidden rounded-md transition-transform duration-300 ease-out;
  }

  .bento-title {
    @apply uppercase md:text-6xl text-4xl font-black font-zentry;
  }

  .story-img-container {
    @apply relative md:h-dvh h-[90vh] w-full;
    filter: url("#flt_tag");
  }

  .story-img-mask {
    @apply absolute left-0 top-0 size-full overflow-hidden md:left-[20%] md:top-[-10%] md:size-4/5;
    clip-path: polygon(4% 0, 83% 21%, 100% 73%, 0% 100%);
  }

  .story-img-content {
    @apply absolute w-full md:h-dvh h-[50dvh] opacity-100 left-10 top-16 md:left-0 md:top-10 lg:left-[-300px] lg:top-[-100px];
    transform: translate3d(0, 0, 0) rotateX(0) rotateY(0) rotateZ(0) scale(1);
  }

  .gallery-img-container {
    @apply size-64 overflow-hidden bg-violet-300;
  }

  .gallery-img {
    @apply size-full bg-cover;
  }

  .gallery-img-4 {
    @apply sm:size-80 md:h-96 md:w-[25rem] rounded-lg;
  }

  .sword-man-clip-path {
    clip-path: polygon(16% 0, 89% 15%, 75% 100%, 0 97%);
  }

  .contact-clip-path-1 {
    clip-path: polygon(25% 0%, 74% 0, 69% 64%, 34% 73%);
  }

  .contact-clip-path-2 {
    clip-path: polygon(29% 15%, 85% 30%, 50% 100%, 10% 64%);
  }
}

.indicator-line {
  @apply h-1 w-px rounded-full bg-white transition-all duration-200 ease-in-out;
}

.indicator-line.active {
  animation: indicator-line 0.5s ease infinite;
  animation-delay: calc(var(--animation-order) * 0.1s);
}

@keyframes indicator-line {
  0% {
    height: 4px;
    transform: translateY(-0px);
  }
  50% {
    height: 16px;
    transform: translateY(-4px);
  }
  100% {
    height: 4px;
    transform: translateY(-0px);
  }
}

/* From Uiverse.io by G4b413l */
/* https://uiverse.io/G4b413l/tidy-walrus-92 */
.three-body {
  --uib-size: 35px;
  --uib-speed: 0.8s;
  --uib-color: #5d3fd3;
  position: relative;
  display: inline-block;
  height: var(--uib-size);
  width: var(--uib-size);
  animation: spin78236 calc(var(--uib-speed) * 2.5) infinite linear;
}

.three-body__dot {
  position: absolute;
  height: 100%;
  width: 30%;
}

.three-body__dot:after {
  content: "";
  position: absolute;
  height: 0%;
  width: 100%;
  padding-bottom: 100%;
  background-color: var(--uib-color);
  border-radius: 50%;
}

.three-body__dot:nth-child(1) {
  bottom: 5%;
  left: 0;
  transform: rotate(60deg);
  transform-origin: 50% 85%;
}

.three-body__dot:nth-child(1)::after {
  bottom: 0;
  left: 0;
  animation: wobble1 var(--uib-speed) infinite ease-in-out;
  animation-delay: calc(var(--uib-speed) * -0.3);
}

.three-body__dot:nth-child(2) {
  bottom: 5%;
  right: 0;
  transform: rotate(-60deg);
  transform-origin: 50% 85%;
}

.three-body__dot:nth-child(2)::after {
  bottom: 0;
  left: 0;
  animation: wobble1 var(--uib-speed) infinite calc(var(--uib-speed) * -0.15)
    ease-in-out;
}

.three-body__dot:nth-child(3) {
  bottom: -5%;
  left: 0;
  transform: translateX(116.666%);
}

.three-body__dot:nth-child(3)::after {
  top: 0;
  left: 0;
  animation: wobble2 var(--uib-speed) infinite ease-in-out;
}

@keyframes spin78236 {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

@keyframes wobble1 {
  0%,
  100% {
    transform: translateY(0%) scale(1);
    opacity: 1;
  }

  50% {
    transform: translateY(-66%) scale(0.65);
    opacity: 0.8;
  }
}

@keyframes wobble2 {
  0%,
  100% {
    transform: translateY(0%) scale(1);
    opacity: 1;
  }

  50% {
    transform: translateY(66%) scale(0.65);
    opacity: 0.8;
  }
}

/* Loading Screen Styles */
.hero {
  
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  position: relative;
}

.pre-loader {
  width: 100%;
  height: 100%;
  padding: 2em;
  position: fixed;
  top: 0;
  right: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 0.5em;
  overflow: hidden;
  background: var(--background);
  z-index: 9999;
}

.pre-loader p {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: var(--foreground);
}

.counter {
  display: flex;
  font-size: 3rem;
  height: 3rem;
  overflow: hidden;
  color: var(--foreground);
  font-weight: bold;
  transition: transform 0.5s ease-in-out;
}

.digit-1, .digit-2, .digit-3, .digit-4 {
  position: relative;
  height: 3rem;
  overflow: hidden;
}

.num {
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-bar {
  width: 0%;
  height: 4px;
  background-color: var(--foreground);
  margin-top: 2rem;
  transition: width 0.3s ease;
}

/* Slide left effect when loading completes */
.slide-left .counter {
  transform: translateX(-100%);
  opacity: 0.5; /* Fades slightly while sliding */
}

.slide-left::after {
  content: "";
  width: 80px; /* Thin line after sliding */
  height: 2px;
  background: black;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

/* Loader styles */
.loader_wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background-color: #10061E;
  z-index: 9998;
  pointer-events: none;
}

.loader_gallery {
  display: flex;
  gap: 2rem;
  height: 100vh;
  position: relative;
  z-index: 9996;
  perspective: 2000px;
  transform-style: preserve-3d;
  transform: scale(0.75);
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

/* Ensure body has no scroll during loading */
body.loading {
  overflow: hidden;
  height: 100vh;
  width: 100vw;
}

/* First, reset all pointer-events */
.loader_wrapper,
.loader_gallery,
.loader_gallery_figure,
.loader_gallery_image {
  pointer-events: initial;
}

/* Make the figure a button to force interactivity */
.loader_gallery_figure {
  position: relative;
  width: 130px;
  height: 80vh;
  margin: 0;
  overflow: hidden;
  transform-style: preserve-3d;
  transform-origin: 100% 50%;
  transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.4);
  flex-shrink: 0;
  z-index: 1;
}

/* Wrap image in a container */
.image-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
}

.loader_gallery_image {
  position: relative; /* Changed from absolute */
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
  z-index: 3;
}

/* Remove any pointer-events: none */
.loader_gallery_figure::after {
  content: '';
  position: absolute;
  inset: 0;
  background: radial-gradient(circle at center, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 70%);
  opacity: 0;
  transition: opacity 0.4s ease;
  z-index: 1;
}

/* Base transforms - store the initial rotation */
.loader_gallery_figure:nth-child(1) { --rotate: 45deg; transform: rotateY(var(--rotate)) translateZ(100px); }
.loader_gallery_figure:nth-child(2) { --rotate: 35deg; transform: rotateY(var(--rotate)) translateZ(80px); }
.loader_gallery_figure:nth-child(3) { --rotate: 25deg; transform: rotateY(var(--rotate)) translateZ(60px); }
.loader_gallery_figure:nth-child(4) { --rotate: 15deg; transform: rotateY(var(--rotate)) translateZ(40px); }
.loader_gallery_figure:nth-child(5) { --rotate: 0deg; transform: rotateY(var(--rotate)) translateZ(20px); }
.loader_gallery_figure:nth-child(6) { --rotate: -15deg; transform: rotateY(var(--rotate)) translateZ(40px); }
.loader_gallery_figure:nth-child(7) { --rotate: -25deg; transform: rotateY(var(--rotate)) translateZ(60px); }
.loader_gallery_figure:nth-child(8) { --rotate: -35deg; transform: rotateY(var(--rotate)) translateZ(80px); }
.loader_gallery_figure:nth-child(9) { --rotate: -45deg; transform: rotateY(var(--rotate)) translateZ(100px); }

/* Hover effect */
.loader_gallery_figure:hover {
  transform: rotateY(calc(var(--rotate) * 0.5)) translateZ(100px) translateY(-30px) !important;
  z-index: 25;
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.5);
}

.loader_gallery_image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
  pointer-events: all !important; /* Force enable interactions */
  cursor: default !important; /* Reset cursor */
  z-index: 30; /* Ensure it's above other elements */
}

.loader_gallery_figure:hover .loader_gallery_image {
  filter: brightness(1.2);
  transform: scale(1.1);
}

/* Glow effect */
.loader_gallery_figure::after {
  content: '';
  position: absolute;
  inset: 0;
  background: radial-gradient(circle at center, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 70%);
  opacity: 0;
  transition: opacity 0.4s ease;
  z-index: 1;
}

.loader_gallery_figure:hover::after {
  opacity: 1;
}

/* Grayscale and color transition effects */
.loader_gallery_figure {
  transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
}

.loader_gallery_figure.grayscale {
  filter: grayscale(1) brightness(0.8);
  transition: filter 0.6s ease-out;
}

.loader_gallery_figure.reveal-color {
  filter: grayscale(0) brightness(1);
  transition: filter 0.6s ease-out;
}

/* Center image specific styles */
.loader_gallery_figure.center-image {
  z-index: 10;
  transition: all 0.8s cubic-bezier(0.16, 1, 0.3, 1);
}

/* Animation classes for the loading sequence */
@keyframes revealColor {
  from {
    filter: grayscale(1) brightness(0.8);
  }
  to {
    filter: grayscale(0) brightness(1);
  }
}

.loader_gallery_figure {
  animation: none;
}

.loader_gallery_figure.animate-reveal {
  animation: revealColor 0.6s ease-out forwards;
}

/* Mobile adjustments with grayscale integration */
@media (max-width: 768px) {
  .loader_gallery {
    gap: 2.8rem;
    padding: 0;
    perspective: 4200px;
    width: 420vw;
    position: absolute;
    left: 50%;
    transform-origin: center center;
    transform: translateX(-50%) scale(0.35) translateZ(0);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .loader_gallery_figure {
    width: 168px;
    height: 65vh;
    transform-origin: center center;
    filter: grayscale(1) brightness(0.8); /* Default state */
  }

  /* Base transforms with grayscale management */
  .loader_gallery_figure:nth-child(1) {
    --rotate: 45deg;
    transform: rotateY(var(--rotate)) translateZ(840px);
  }
  .loader_gallery_figure:nth-child(2) {
    --rotate: 35deg;
    transform: rotateY(var(--rotate)) translateZ(700px);
  }
  .loader_gallery_figure:nth-child(3) {
    --rotate: 25deg;
    transform: rotateY(var(--rotate)) translateZ(560px);
  }
  .loader_gallery_figure:nth-child(4) {
    --rotate: 15deg;
    transform: rotateY(var(--rotate)) translateZ(420px);
  }
  .loader_gallery_figure:nth-child(5) {
    --rotate: 0deg;
    transform: rotateY(var(--rotate)) translateZ(280px);
  }
  .loader_gallery_figure:nth-child(6) {
    --rotate: -15deg;
    transform: rotateY(var(--rotate)) translateZ(420px);
  }
  .loader_gallery_figure:nth-child(7) {
    --rotate: -25deg;
    transform: rotateY(var(--rotate)) translateZ(560px);
  }
  .loader_gallery_figure:nth-child(8) {
    --rotate: -35deg;
    transform: rotateY(var(--rotate)) translateZ(700px);
  }
  .loader_gallery_figure:nth-child(9) {
    --rotate: -45deg;
    transform: rotateY(var(--rotate)) translateZ(840px);
  }

  /* Hover effects with color reveal */
  .loader_gallery_figure:hover {
    transform: rotateY(calc(var(--rotate) * 0.5)) translateZ(840px) translateY(-30px) !important;
    z-index: 25;
    filter: grayscale(0) brightness(1.2);
  }

  /* Enhanced image transitions */
  .loader_gallery_image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
  }
}

/* Ensure smooth transitions */
.loader_gallery_figure,
.loader_gallery_image {
  transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
}

/* Enhanced hover effects with color management */
.loader_gallery_figure:hover .loader_gallery_image {
  transform: scale(1.1);
  filter: contrast(1.2) brightness(1.2);
}

/* Glow effect for revealed images */
.loader_gallery_figure.reveal-color::after {
  content: '';
  position: absolute;
  inset: 0;
  background: radial-gradient(circle at center, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 70%);
  opacity: 0.6;
  transition: opacity 0.4s ease;
  z-index: 1;
}

/* Ensure proper 3D rendering */
.loader_gallery {
  transform-style: preserve-3d;
}

.loader_gallery_figure {
  transform-style: preserve-3d;
  backface-visibility: hidden;
  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.4);
}

/* Enhanced hover effects */
.loader_gallery_figure:hover .loader_gallery_image {
  transform: scale(1.1);
  filter: contrast(1.2) brightness(1.2);
}

/* Add these animations to your existing globals.css */

@keyframes gradient-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.animate-gradient-shift {
  background-size: 200% 200%;
  animation: gradient-shift 15s ease infinite;
}

@keyframes text-shimmer {
  0% { background-position: -200% 50%; }
  100% { background-position: 200% 50%; }
}

.animate-text-shimmer {
  background-size: 200% 200%;
  animation: text-shimmer 3s linear infinite;
}

.perspective { /* Consolidate with .loader_wrapper's perspective or use distinct classes */
  perspective: 1500px;
}

.flip-card {
  transform-style: preserve-3d;
  transition: transform 0.7s ease;
}

/* .rotate-y-180 is defined twice, remove one */
.rotate-y-180 {
  transform: rotateY(180deg);
}

.card-face {
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

.card-back {
  transform: rotateY(180deg);
}

.transform-style-3d { /* Generic class, ensure it doesn't conflict if also on loader elements */
  transform-style: preserve-3d;
}

.backface-hidden {
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

/* New squared navigation buttons */
.trendy-nav-btn {
  width: 40px !important;
  height: 40px !important;
  background: rgba(255, 255, 255, 0.1) !important;
  transition: all 0.3s ease !important;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.trendy-nav-btn:hover {
  background: rgba(255, 255, 255, 0.2) !important;
  transform: scale(1.05);
}

.trendy-nav-btn:active {
  transform: scale(0.95);
}

.trendy-nav-btn svg {
  transition: transform 0.2s ease;
}

.trendy-nav-btn:hover svg {
  transform: scale(1.1);
}

.trendy-nav-btn.swiper-button-disabled {
  opacity: 0.5 !important;
  cursor: not-allowed !important;
}

/* Remove default Swiper navigation styles */
.trendy-games-swiper .swiper-button-next,
.trendy-games-swiper .swiper-button-prev {
  position: static !important;
  margin: 0 !important;
}

/* Optional: Add a gradient fade effect on the edges */
.trendy-games-container {
  position: relative;
}

.trendy-games-container::before,
.trendy-games-container::after {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  width: 100px;
  z-index: 2;
  pointer-events: none;
}

/* Shimmering text gradient animation */
.text-gradient-shine { /* You have two definitions of this, one earlier with Tailwind @apply */
                        /* Choose one or make them distinct. This one will override the @apply version */
  background: linear-gradient(
    to right,
    #4facfe 20%,
    #00f2fe 30%,
    #a6ffcb 70%,
    #4facfe 80%
  );
  
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  background-size: 200% auto;
  animation: textShine 5s linear infinite;
}

@keyframes textShine {
  0% {
    background-position: 200% center;
  }
  100% {
    background-position: -200% center;
  }
}

/* About section specific styles */
.about-section-particle {
  pointer-events: none;
  will-change: transform;
}

/* Enhance blur shadow effect */
.blur-shadow {
  filter: blur(40px);
  mix-blend-mode: screen;
}

/* Smooth transitions for text scaling */
.text-scale-transition {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.nexus__sticky {
  z-index: 1;
  background-color: #10061E; /* Consider using a CSS variable if you have one for this color */
}

.nexus-item {
  transition: opacity 0.3s ease;
}

.nexus-item:not(.active) {
  opacity: 0.5;
}

.nexus-item.active {
  opacity: 1;
}

.nexus-item__progress-bar {
  transition: transform 0.3s ease;
}

.perspective-transition { /* Generic, if used on loader parent, it's like .loader_wrapper's perspective */
  perspective: 1000px;
}

.btn-main {
  position: relative;
  overflow: hidden;
}

.btn-main::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s;
}

.btn-main:hover::before {
  transform: translateX(100%);
}

.section-active {
  opacity: 1 !important;
  transform: translateY(0) !important;
}

/* Optional: Add smooth scrolling to the whole page */
html {
  scroll-behavior: smooth;
}

@media (max-width: 639px) {
  .animated-title {
    font-size: 25px !important;
  }
}
